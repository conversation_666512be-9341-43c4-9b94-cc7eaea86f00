# Dr. Resume Production Requirements for Heroku
# This file should be in the root directory for Heroku deployment

# Core Flask Framework
Flask==2.3.3
Flask-CORS==4.0.0

# Database & ORM
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5
psycopg2-binary==2.9.7

# Authentication & Security
Flask-JWT-Extended==4.5.3
Werkzeug==2.3.7
bcrypt==4.0.1

# File Processing
PyPDF2==3.0.1
python-docx==0.8.11

# NLP & Text Analysis (REQUIRED for Local AI)
nltk==3.8.1
spacy>=3.6.1
scikit-learn==1.3.0
numpy==1.24.3

# Similarity & Matching Algorithms
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# Environment & Configuration
python-dotenv==1.0.0

# AI & Premium Features (Optional)
openai>=1.0.0

# Production Server
gunicorn==21.2.0

# Additional dependencies
typing-extensions>=4.0.0
