@echo off
echo ========================================
echo    Dr. Resume - Heroku Deployment
echo ========================================
echo.

echo Step 1: Checking Git repository...
if not exist .git (
    echo Initializing Git repository...
    git init
    git add .
    git commit -m "Initial commit - Dr. Resume ready for Heroku deployment"
) else (
    echo Git repository exists. Adding latest changes...
    git add .
    git commit -m "Updated for Heroku deployment"
)

echo.
echo Step 2: Login to Heroku...
echo Please complete the login process in your browser.
heroku login

echo.
echo Step 3: Creating Heroku application...
set /p APP_NAME="Enter your desired app name (or press Enter for random name): "
if "%APP_NAME%"=="" (
    heroku create
) else (
    heroku create %APP_NAME%
)

echo.
echo Step 4: Adding PostgreSQL database...
heroku addons:create heroku-postgresql:mini

echo.
echo Step 5: Setting environment variables...
heroku config:set FLASK_ENV=production

echo Please set your secret keys (IMPORTANT: Use secure random values!)
set /p SECRET_KEY="Enter SECRET_KEY (or press Enter for default): "
if "%SECRET_KEY%"=="" set SECRET_KEY=change-this-to-secure-random-value-in-production
heroku config:set SECRET_KEY="%SECRET_KEY%"

set /p JWT_SECRET="Enter JWT_SECRET_KEY (or press Enter for default): "
if "%JWT_SECRET%"=="" set JWT_SECRET=change-this-jwt-secret-to-secure-random-value
heroku config:set JWT_SECRET_KEY="%JWT_SECRET%"

set /p OPENAI_KEY="Enter OpenAI API Key (optional, press Enter to skip): "
if not "%OPENAI_KEY%"=="" heroku config:set OPENAI_API_KEY="%OPENAI_KEY%"

echo.
echo Step 6: Deploying to Heroku...
git push heroku main

echo.
echo Step 7: Initializing database...
heroku run python us10/backend/init_heroku_db.py

echo.
echo Step 8: Opening your application...
heroku open

echo.
echo ========================================
echo   Deployment Complete! 🎉
echo ========================================
echo.
echo Your Dr. Resume application is now live!
echo.
echo Useful commands:
echo   heroku logs --tail    (view logs)
echo   heroku restart        (restart app)
echo   heroku config         (view settings)
echo.
pause
