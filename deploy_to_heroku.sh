#!/bin/bash

echo "========================================"
echo "   Dr. Resume - Heroku Deployment"
echo "========================================"
echo

echo "Step 1: Checking Git repository..."
if [ ! -d ".git" ]; then
    echo "Initializing Git repository..."
    git init
    git add .
    git commit -m "Initial commit - Dr. Resume ready for Heroku deployment"
else
    echo "Git repository exists. Adding latest changes..."
    git add .
    git commit -m "Updated for Heroku deployment"
fi

echo
echo "Step 2: Login to Heroku..."
echo "Please complete the login process in your browser."
heroku login

echo
echo "Step 3: Creating Heroku application..."
read -p "Enter your desired app name (or press Enter for random name): " APP_NAME
if [ -z "$APP_NAME" ]; then
    heroku create
else
    heroku create "$APP_NAME"
fi

echo
echo "Step 4: Adding PostgreSQL database..."
heroku addons:create heroku-postgresql:mini

echo
echo "Step 5: Setting environment variables..."
heroku config:set FLASK_ENV=production

echo "Please set your secret keys (IMPORTANT: Use secure random values!)"
read -p "Enter SECRET_KEY (or press Enter for default): " SECRET_KEY
if [ -z "$SECRET_KEY" ]; then
    SECRET_KEY="change-this-to-secure-random-value-in-production"
fi
heroku config:set SECRET_KEY="$SECRET_KEY"

read -p "Enter JWT_SECRET_KEY (or press Enter for default): " JWT_SECRET
if [ -z "$JWT_SECRET" ]; then
    JWT_SECRET="change-this-jwt-secret-to-secure-random-value"
fi
heroku config:set JWT_SECRET_KEY="$JWT_SECRET"

read -p "Enter OpenAI API Key (optional, press Enter to skip): " OPENAI_KEY
if [ ! -z "$OPENAI_KEY" ]; then
    heroku config:set OPENAI_API_KEY="$OPENAI_KEY"
fi

echo
echo "Step 6: Deploying to Heroku..."
git push heroku main

echo
echo "Step 7: Initializing database..."
heroku run python us10/backend/init_heroku_db.py

echo
echo "Step 8: Opening your application..."
heroku open

echo
echo "========================================"
echo "   Deployment Complete! 🎉"
echo "========================================"
echo
echo "Your Dr. Resume application is now live!"
echo
echo "Useful commands:"
echo "  heroku logs --tail    (view logs)"
echo "  heroku restart        (restart app)"
echo "  heroku config         (view settings)"
echo
