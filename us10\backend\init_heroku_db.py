#!/usr/bin/env python3
"""
Initialize database for Heroku deployment
Run this script after deploying to Heroku to set up the database tables
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from app_fixed import create_app
from models import db

def init_database():
    """Initialize the database with all tables"""
    print("🗄️ Initializing Heroku database...")
    
    try:
        app = create_app()
        
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Print table info
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📊 Created {len(tables)} tables: {', '.join(tables)}")
            
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    init_database()
