# 🚀 Dr. Resume - Complete Heroku Deployment Guide

## 📋 Prerequisites
- ✅ Heroku account created
- ✅ Heroku CLI installed
- ✅ Git installed on your system

## 🗂️ Files Created for Deployment
The following files have been created/modified for Heroku deployment:

1. **Procfile** - Tells <PERSON><PERSON> how to run your app
2. **requirements.txt** - Python dependencies (root directory)
3. **runtime.txt** - Specifies Python version
4. **wsgi.py** - WSGI entry point for production
5. **.gitignore** - Excludes sensitive files from Git
6. **.env.example** - Template for environment variables
7. **Modified config.py** - PostgreSQL support for production
8. **Modified app_fixed.py** - Production-ready configuration

## 🔧 Step-by-Step Deployment Process

### Step 1: Prepare Your Local Repository
```bash
# Navigate to your project directory
cd "C:\Users\<USER>\OneDrive\Desktop\Dr.Resume"

# Initialize Git repository (if not already done)
git init

# Add all files to Git
git add .

# Commit your changes
git commit -m "Initial commit - Dr. Resume ready for Heroku deployment"
```

### Step 2: Login to Heroku
```bash
# Login to Heroku CLI
heroku login
```
This will open your browser for authentication.

### Step 3: Create Heroku Application
```bash
# Create a new Heroku app (replace 'your-app-name' with your desired name)
heroku create your-dr-resume-app

# Or let Heroku generate a random name
heroku create
```

### Step 4: Add PostgreSQL Database
```bash
# Add free PostgreSQL database to your Heroku app
heroku addons:create heroku-postgresql:mini
```

### Step 5: Set Environment Variables
```bash
# Set production environment
heroku config:set FLASK_ENV=production

# Set secret keys (CHANGE THESE TO SECURE RANDOM VALUES!)
heroku config:set SECRET_KEY="your-super-secret-key-here-change-this"
heroku config:set JWT_SECRET_KEY="your-jwt-secret-key-here-change-this"

# Optional: Set OpenAI API key for premium features
heroku config:set OPENAI_API_KEY="your-openai-api-key-if-you-have-one"

# Set Heroku app name for CORS (replace with your actual app name)
heroku config:set HEROKU_APP_NAME="your-dr-resume-app"
```

### Step 6: Deploy to Heroku
```bash
# Deploy your application
git push heroku main

# If you're on a different branch (like master), use:
# git push heroku master
```

### Step 7: Initialize Database
```bash
# Run database initialization
heroku run python us10/backend/init_heroku_db.py

# Check if it worked
heroku logs --tail
```

### Step 8: Open Your Application
```bash
# Open your deployed application
heroku open
```

## 🔍 Troubleshooting

### Common Issues and Solutions:

1. **Build Failed - Python Version**
   ```bash
   # Check your runtime.txt has the correct Python version
   cat runtime.txt
   ```

2. **Database Connection Issues**
   ```bash
   # Check database URL is set
   heroku config:get DATABASE_URL
   ```

3. **Application Crashes**
   ```bash
   # Check application logs
   heroku logs --tail
   
   # Check specific process logs
   heroku logs --source app --tail
   ```

4. **Static Files Not Loading**
   - Ensure your frontend files are in `us10/frontend/`
   - Check that Flask is configured to serve static files

### Useful Heroku Commands:
```bash
# View app information
heroku info

# View configuration variables
heroku config

# View recent logs
heroku logs

# Restart the application
heroku restart

# Run commands on Heroku
heroku run python --version

# Scale dynos (free tier allows 1)
heroku ps:scale web=1
```

## 🎯 Post-Deployment Checklist

- [ ] Application loads without errors
- [ ] Database tables are created
- [ ] User registration works
- [ ] User login works
- [ ] File upload functionality works
- [ ] All API endpoints respond correctly

## 🔒 Security Notes

1. **Change Default Secret Keys**: The secret keys in the deployment are placeholders. Change them to secure random values.

2. **Environment Variables**: Never commit `.env` files or expose secret keys in your code.

3. **Database**: Heroku PostgreSQL is automatically secured and managed.

## 💰 Cost Information

- **Heroku Dyno**: Free tier available (sleeps after 30 minutes of inactivity)
- **PostgreSQL**: Mini plan is free (10,000 rows limit)
- **Total Cost**: $0 for basic usage

## 🚀 Going Live

Your Dr. Resume application will be available at:
`https://your-app-name.herokuapp.com`

The application includes:
- User registration and authentication
- Resume upload and parsing
- Job description analysis
- Keyword extraction
- Matching algorithms
- AI-powered suggestions
- Dashboard analytics

## 📞 Support

If you encounter issues:
1. Check the logs: `heroku logs --tail`
2. Verify environment variables: `heroku config`
3. Ensure database is properly initialized
4. Check that all required files are committed to Git
